#!/bin/bash

# GUI环境诊断脚本
# 用于检查Ubuntu桌面环境中GUI程序运行所需的环境

echo "=== GUI环境诊断工具 ==="
echo "检查时间: $(date)"
echo

# 1. 检查显示环境变量
echo "1. 显示环境变量检查:"
echo "DISPLAY = ${DISPLAY:-未设置}"
echo "WAYLAND_DISPLAY = ${WAYLAND_DISPLAY:-未设置}"
echo "XDG_SESSION_TYPE = ${XDG_SESSION_TYPE:-未设置}"
echo

# 2. 检查显示服务器
echo "2. 显示服务器检查:"
if command -v xset >/dev/null 2>&1; then
    if xset q >/dev/null 2>&1; then
        echo "✓ X11服务器运行正常"
        echo "X11服务器信息:"
        xset q | head -3
    else
        echo "✗ X11服务器连接失败"
    fi
else
    echo "? xset命令不可用，无法检查X11"
fi

if [ -n "$WAYLAND_DISPLAY" ]; then
    echo "✓ 检测到Wayland显示服务器"
else
    echo "? 未检测到Wayland显示服务器"
fi
echo

# 3. 检查OpenCV依赖
echo "3. OpenCV依赖检查:"
if pkg-config --exists opencv4; then
    echo "✓ OpenCV4已安装"
    echo "版本: $(pkg-config --modversion opencv4)"
    
    # 检查GUI相关模块
    if pkg-config --exists opencv4 && pkg-config --libs opencv4 | grep -q "highgui"; then
        echo "✓ OpenCV highgui模块可用"
    else
        echo "? OpenCV highgui模块状态未知"
    fi
else
    echo "✗ OpenCV4未找到"
fi
echo

# 4. 检查图形库依赖
echo "4. 图形库依赖检查:"
libs=("libgtk-3-0" "libgtk2.0-0" "libqt5gui5" "libx11-6")
for lib in "${libs[@]}"; do
    if dpkg -l | grep -q "^ii.*$lib"; then
        echo "✓ $lib 已安装"
    else
        echo "? $lib 未安装或未找到"
    fi
done
echo

# 5. 测试简单的GUI程序
echo "5. GUI程序测试:"
if command -v xeyes >/dev/null 2>&1; then
    echo "测试xeyes程序..."
    timeout 2 xeyes >/dev/null 2>&1 &
    sleep 1
    if pgrep xeyes >/dev/null; then
        echo "✓ xeyes可以启动（GUI环境正常）"
        pkill xeyes 2>/dev/null
    else
        echo "✗ xeyes无法启动"
    fi
else
    echo "? xeyes不可用，无法测试"
fi

# 6. 权限检查
echo
echo "6. 权限检查:"
echo "当前用户: $(whoami)"
echo "用户组: $(groups)"
if groups | grep -q video; then
    echo "✓ 用户在video组中"
else
    echo "? 用户不在video组中"
fi
echo

# 7. 建议
echo "7. 问题解决建议:"
if [ -z "$DISPLAY" ] && [ -z "$WAYLAND_DISPLAY" ]; then
    echo "⚠ 未检测到显示环境，请确保在图形桌面环境中运行"
elif [ "$XDG_SESSION_TYPE" = "wayland" ]; then
    echo "⚠ 检测到Wayland环境，可能需要特殊配置"
    echo "  建议尝试: export GDK_BACKEND=x11"
    echo "  或切换到X11会话"
else
    echo "✓ 显示环境看起来正常"
fi

echo
echo "=== 诊断完成 ==="
