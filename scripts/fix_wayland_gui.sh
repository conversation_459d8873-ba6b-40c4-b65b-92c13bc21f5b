#!/bin/bash

# Wayland环境下OpenCV GUI兼容性修复脚本

echo "=== Wayland OpenCV GUI兼容性修复工具 ==="
echo "检测时间: $(date)"
echo

# 检查当前环境
DISPLAY_VAR="${DISPLAY:-}"
WAYLAND_DISPLAY_VAR="${WAYLAND_DISPLAY:-}"
SESSION_TYPE="${XDG_SESSION_TYPE:-}"
GDK_BACKEND_VAR="${GDK_BACKEND:-}"

echo "当前环境状态:"
echo "  DISPLAY = ${DISPLAY_VAR:-未设置}"
echo "  WAYLAND_DISPLAY = ${WAYLAND_DISPLAY_VAR:-未设置}"
echo "  XDG_SESSION_TYPE = ${SESSION_TYPE:-未设置}"
echo "  GDK_BACKEND = ${GDK_BACKEND_VAR:-未设置}"
echo

# 检测Wayland环境
if [ "$SESSION_TYPE" = "wayland" ] || [ -n "$WAYLAND_DISPLAY_VAR" ]; then
    echo "🔍 检测到Wayland环境"
    echo
    
    echo "应用Wayland兼容性修复..."
    
    # 设置环境变量以强制使用X11后端
    export GDK_BACKEND=x11
    export QT_QPA_PLATFORM=xcb
    
    # 确保DISPLAY变量设置
    if [ -z "$DISPLAY_VAR" ]; then
        export DISPLAY=:0
        echo "✓ 设置 DISPLAY=:0"
    fi
    
    echo "✓ 设置 GDK_BACKEND=x11"
    echo "✓ 设置 QT_QPA_PLATFORM=xcb"
    
    # 验证X11服务器可用性
    if command -v xset >/dev/null 2>&1; then
        if xset q >/dev/null 2>&1; then
            echo "✓ X11服务器连接成功"
        else
            echo "⚠️  X11服务器连接失败，但继续尝试"
        fi
    fi
    
    echo
    echo "🧪 测试Wayland兼容性修复..."
    
    # 编译并运行Wayland测试程序
    cd "$(dirname "$0")/.."
    
    if [ ! -f "test_wayland_gui" ]; then
        echo "编译Wayland测试程序..."
        g++ -std=c++17 test_wayland_gui.cpp -o test_wayland_gui \
            $(pkg-config --cflags --libs opencv4) \
            -pthread
        
        if [ $? -ne 0 ]; then
            echo "❌ 编译失败"
            exit 1
        fi
    fi
    
    echo "运行Wayland兼容性测试..."
    ./test_wayland_gui
    
    if [ $? -eq 0 ]; then
        echo
        echo "🎉 Wayland兼容性修复成功！"
        echo
        echo "现在可以运行调试工具:"
        echo "  cd build"
        echo "  ./bin/debug_tool"
        echo
        echo "如果仍有问题，请尝试以下命令:"
        echo "  export GDK_BACKEND=x11"
        echo "  export QT_QPA_PLATFORM=xcb"
        echo "  export DISPLAY=:0"
        echo "  ./bin/debug_tool"
    else
        echo
        echo "❌ Wayland兼容性测试失败"
        echo
        echo "备选解决方案:"
        echo "1. 切换到X11会话:"
        echo "   - 注销当前会话"
        echo "   - 在登录界面选择 'Ubuntu on Xorg'"
        echo
        echo "2. 使用命令行模式:"
        echo "   ./bin/debug_tool --single image.jpg"
        echo "   ./bin/debug_tool --batch ./images"
        echo
        echo "3. 手动设置环境变量:"
        echo "   export GDK_BACKEND=x11"
        echo "   export QT_QPA_PLATFORM=xcb"
        echo "   export DISPLAY=:0"
    fi
    
else
    echo "✓ 当前环境不是Wayland，无需修复"
    
    # 仍然运行基本的GUI测试
    echo "运行基本GUI测试..."
    cd "$(dirname "$0")/.."
    
    if [ -f "test_opencv_gui" ]; then
        ./test_opencv_gui
    else
        echo "编译并运行GUI测试..."
        ./scripts/build_gui_test.sh
        ./test_opencv_gui
    fi
fi

echo
echo "=== 修复完成 ==="
