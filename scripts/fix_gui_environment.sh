#!/bin/bash

# GUI环境快速修复脚本
# 自动检测并尝试修复GUI显示问题

echo "=== GUI环境快速修复工具 ==="
echo "检测时间: $(date)"
echo

# 检查当前环境
DISPLAY_VAR="${DISPLAY:-}"
WAYLAND_DISPLAY_VAR="${WAYLAND_DISPLAY:-}"
SESSION_TYPE="${XDG_SESSION_TYPE:-}"

echo "当前环境状态:"
echo "  DISPLAY = ${DISPLAY_VAR:-未设置}"
echo "  WAYLAND_DISPLAY = ${WAYLAND_DISPLAY_VAR:-未设置}"
echo "  XDG_SESSION_TYPE = ${SESSION_TYPE:-未设置}"
echo

# 检测问题类型
if [ -z "$DISPLAY_VAR" ] && [ -z "$WAYLAND_DISPLAY_VAR" ]; then
    if [ "$SESSION_TYPE" = "tty" ]; then
        echo "🔍 检测到问题: 当前在文本终端(TTY)模式"
        echo
        echo "解决方案选项:"
        echo "1. 启动图形桌面环境 (推荐)"
        echo "2. 切换到已运行的图形界面"
        echo "3. 安装桌面环境 (如果未安装)"
        echo "4. 使用虚拟显示"
        echo "5. 退出并手动处理"
        echo
        
        read -p "请选择解决方案 (1-5): " choice
        
        case $choice in
            1)
                echo "尝试启动图形桌面环境..."
                
                # 检查可用的显示管理器
                if systemctl is-installed gdm3 >/dev/null 2>&1; then
                    echo "启动GDM3 (GNOME)..."
                    sudo systemctl start gdm3
                elif systemctl is-installed lightdm >/dev/null 2>&1; then
                    echo "启动LightDM..."
                    sudo systemctl start lightdm
                elif systemctl is-installed sddm >/dev/null 2>&1; then
                    echo "启动SDDM (KDE)..."
                    sudo systemctl start sddm
                else
                    echo "❌ 未找到显示管理器"
                    echo "请先安装桌面环境: sudo apt install ubuntu-desktop-minimal"
                    exit 1
                fi
                
                echo "✅ 图形界面启动命令已执行"
                echo "请切换到图形界面 (Ctrl+Alt+F7) 然后重新运行调试工具"
                ;;
                
            2)
                echo "尝试切换到图形界面..."
                echo "请按以下快捷键切换:"
                echo "  Ctrl+Alt+F1  或  Ctrl+Alt+F7"
                echo "如果看到图形界面，请在那里运行调试工具"
                ;;
                
            3)
                echo "安装桌面环境..."
                echo "选择要安装的桌面环境:"
                echo "1. Ubuntu Desktop (完整)"
                echo "2. Ubuntu Desktop Minimal (推荐)"
                echo "3. XFCE (轻量级)"
                
                read -p "请选择 (1-3): " desktop_choice
                
                case $desktop_choice in
                    1)
                        sudo apt update
                        sudo apt install -y ubuntu-desktop
                        ;;
                    2)
                        sudo apt update
                        sudo apt install -y ubuntu-desktop-minimal
                        ;;
                    3)
                        sudo apt update
                        sudo apt install -y xfce4 xfce4-goodies
                        ;;
                    *)
                        echo "无效选择"
                        exit 1
                        ;;
                esac
                
                echo "✅ 桌面环境安装完成"
                echo "启动图形界面..."
                sudo systemctl start gdm3
                ;;
                
            4)
                echo "设置虚拟显示..."
                
                # 安装xvfb
                if ! command -v Xvfb >/dev/null 2>&1; then
                    echo "安装虚拟显示支持..."
                    sudo apt update
                    sudo apt install -y xvfb
                fi
                
                # 启动虚拟显示
                export DISPLAY=:99
                echo "启动虚拟显示服务器..."
                Xvfb :99 -screen 0 1024x768x24 >/dev/null 2>&1 &
                XVFB_PID=$!
                
                sleep 2
                
                echo "✅ 虚拟显示已启动 (DISPLAY=:99)"
                echo "现在可以运行GUI程序了"
                
                # 测试虚拟显示
                echo "测试虚拟显示..."
                cd "$(dirname "$0")/.."
                if [ -f "test_opencv_gui" ]; then
                    echo "运行OpenCV GUI测试..."
                    ./test_opencv_gui
                else
                    echo "编译并运行GUI测试..."
                    ./scripts/build_gui_test.sh
                    ./test_opencv_gui
                fi
                
                # 清理
                echo "清理虚拟显示..."
                kill $XVFB_PID 2>/dev/null
                ;;
                
            5)
                echo "退出修复工具"
                echo "请手动解决GUI环境问题后重新运行调试工具"
                exit 0
                ;;
                
            *)
                echo "无效选择"
                exit 1
                ;;
        esac
        
    else
        echo "🔍 检测到问题: 显示环境变量未设置"
        echo "尝试自动修复..."
        
        # 尝试设置DISPLAY
        if [ -z "$DISPLAY_VAR" ]; then
            export DISPLAY=:0
            echo "设置 DISPLAY=:0"
        fi
        
        # 测试修复结果
        if xset q >/dev/null 2>&1; then
            echo "✅ X11连接成功"
        else
            echo "❌ X11连接仍然失败"
            echo "请检查图形服务器是否运行"
        fi
    fi
    
else
    echo "✅ 显示环境变量已设置"
    
    # 测试连接
    if xset q >/dev/null 2>&1; then
        echo "✅ X11连接正常"
    elif [ -n "$WAYLAND_DISPLAY_VAR" ]; then
        echo "✅ Wayland环境检测到"
        echo "如果遇到问题，尝试: export GDK_BACKEND=x11"
    else
        echo "❌ 显示服务器连接失败"
    fi
fi

echo
echo "=== 修复完成 ==="
echo "建议运行以下命令验证修复结果:"
echo "1. ./scripts/diagnose_gui.sh  # 重新诊断环境"
echo "2. ./test_opencv_gui          # 测试OpenCV GUI"
echo "3. ./bin/debug_tool           # 运行调试工具"
