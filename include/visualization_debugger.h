#pragma once

#include "common.h"
#include "damage_detection_engine.h"
#ifdef USE_OPENCV
#include <opencv2/opencv.hpp>
#include <vector>
#include <string>
#include <map>
#include <memory>

/**
 * @brief 可视化调试器类
 * 
 * 提供缺损检测算法的可视化调试功能，包括：
 * - 检测结果可视化（边界框、标签、置信度）
 * - 中间处理步骤显示
 * - 参数调整和实时预览
 * - 批量测试和结果对比
 * - 调试信息导出
 */
class VisualizationDebugger {
public:
    /**
     * @brief 调试模式枚举
     */
    enum class DebugMode {
        NORMAL,          // 普通模式：只显示最终结果
        INTERMEDIATE,    // 中间结果模式：显示处理步骤
        COMPARISON,      // 对比模式：显示前后对比
        BATCH_TEST       // 批量测试模式
    };

    /**
     * @brief 可视化配置结构
     */
    struct VisualizationConfig {
        // 边界框绘制配置
        int boxThickness = 2;
        double fontScale = 0.6;
        int fontThickness = 2;
        
        // 颜色配置（BGR格式）
        std::map<DamageType, cv::Scalar> damageColors = {
            {DamageType::CRACK, cv::Scalar(0, 0, 255)},      // 红色
            {DamageType::WEAR, cv::Scalar(0, 255, 0)},       // 绿色
            {DamageType::SCRATCH, cv::Scalar(255, 0, 0)},    // 蓝色
            {DamageType::PIT, cv::Scalar(0, 255, 255)},      // 黄色
            {DamageType::BULGE, cv::Scalar(255, 0, 255)},    // 品红色
            {DamageType::AGING, cv::Scalar(255, 255, 0)},    // 青色
            {DamageType::INSTALL_DAMAGE, cv::Scalar(128, 0, 128)} // 紫色
        };
        
        // 显示配置
        bool showBoundingBox = true;
        bool showConfidence = true;
        bool showDamageType = true;
        bool showCenter = false;
        double confidenceThreshold = 0.3;
        
        // 窗口配置
        int windowWidth = 1200;
        int windowHeight = 800;
        std::string windowName = "Damage Detection Debugger";
    };

    /**
     * @brief 中间结果结构
     */
    struct IntermediateResults {
        cv::Mat originalImage;
        cv::Mat preprocessedImage;
        std::map<DamageType, cv::Mat> algorithmOutputs;
        std::map<DamageType, std::vector<DamageResult>> rawResults;
        std::vector<DamageResult> finalResults;
        double processingTime;
        
        IntermediateResults() : processingTime(0.0) {}
    };

public:
    VisualizationDebugger();
    ~VisualizationDebugger();

    /**
     * @brief 初始化调试器
     * @param engine 检测引擎指针
     * @return 初始化是否成功
     */
    bool initialize(std::shared_ptr<DamageDetectionEngine> engine);

    /**
     * @brief 设置调试模式
     * @param mode 调试模式
     */
    void setDebugMode(DebugMode mode);

    /**
     * @brief 获取当前调试模式
     * @return 当前调试模式
     */
    DebugMode getDebugMode() const { return debugMode_; }

    /**
     * @brief 设置可视化配置
     * @param config 可视化配置
     */
    void setVisualizationConfig(const VisualizationConfig& config);

    /**
     * @brief 获取可视化配置
     * @return 当前可视化配置
     */
    const VisualizationConfig& getVisualizationConfig() const { return config_; }

    /**
     * @brief 处理单张图像并显示结果
     * @param image 输入图像
     * @param cameraId 摄像头ID
     * @return 处理是否成功
     */
    bool processAndVisualize(const cv::Mat& image, int cameraId = 0);

    /**
     * @brief 从文件加载图像并处理
     * @param imagePath 图像文件路径
     * @param cameraId 摄像头ID
     * @return 处理是否成功
     */
    bool loadAndProcess(const std::string& imagePath, int cameraId = 0);

    /**
     * @brief 批量处理图像
     * @param imagePaths 图像文件路径列表
     * @param outputDir 输出目录
     * @return 处理成功的图像数量
     */
    int batchProcess(const std::vector<std::string>& imagePaths, 
                     const std::string& outputDir = "output/debug_results");

    /**
     * @brief 绘制检测结果到图像上
     * @param image 输入图像
     * @param results 检测结果
     * @param enabledTypes 启用显示的损伤类型
     * @return 绘制后的图像
     */
    cv::Mat drawResults(const cv::Mat& image, 
                       const std::vector<DamageResult>& results,
                       const std::vector<DamageType>& enabledTypes = {});

    /**
     * @brief 显示中间处理结果
     * @param results 中间结果
     */
    void showIntermediateResults(const IntermediateResults& results);

    /**
     * @brief 保存当前可视化结果
     * @param outputPath 输出文件路径
     * @return 保存是否成功
     */
    bool saveCurrentResult(const std::string& outputPath);

    /**
     * @brief 获取最近的检测结果
     * @return 最近的检测结果
     */
    const std::vector<DamageResult>& getLastResults() const { return lastResults_; }

    /**
     * @brief 获取最近的中间结果
     * @return 最近的中间结果
     */
    const IntermediateResults& getLastIntermediateResults() const { return lastIntermediateResults_; }

    /**
     * @brief 生成检测报告
     * @param results 检测结果列表
     * @param outputPath 报告输出路径
     * @return 生成是否成功
     */
    bool generateReport(const std::vector<std::vector<DamageResult>>& results,
                       const std::string& outputPath);

private:
    std::shared_ptr<DamageDetectionEngine> engine_;
    DebugMode debugMode_;
    VisualizationConfig config_;
    
    // 最近的处理结果
    cv::Mat lastImage_;
    std::vector<DamageResult> lastResults_;
    IntermediateResults lastIntermediateResults_;
    
    // 内部辅助方法
    cv::Mat createInfoPanel(const std::vector<DamageResult>& results);
    std::string formatDamageInfo(const DamageResult& result);
    cv::Scalar getDamageColor(DamageType type);
    void drawBoundingBox(cv::Mat& image, const DamageResult& result);
    void drawLabel(cv::Mat& image, const DamageResult& result);
    cv::Mat resizeForDisplay(const cv::Mat& image, int maxWidth = 800, int maxHeight = 600);

    // 显示方法
    void showNormalResults(const cv::Mat& image, const std::vector<DamageResult>& results);
    void showComparisonResults(const cv::Mat& image, const std::vector<DamageResult>& results);
    void collectIntermediateResults(const cv::Mat& image, const std::vector<DamageResult>& results, double processingTime);
    
    // 统计信息
    struct Statistics {
        int totalImagesProcessed = 0;
        int totalDamagesDetected = 0;
        double avgProcessingTime = 0.0;
        std::map<DamageType, int> damageTypeCounts;
        
        void reset() {
            totalImagesProcessed = 0;
            totalDamagesDetected = 0;
            avgProcessingTime = 0.0;
            damageTypeCounts.clear();
        }
    } stats_;
    
    void updateStatistics(const std::vector<DamageResult>& results, double processingTime);
};

#endif // USE_OPENCV
