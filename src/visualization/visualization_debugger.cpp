#include "../../include/visualization_debugger.h"
#include "../../include/common.h"
#ifdef USE_OPENCV
#include <filesystem>
#include <fstream>
#include <iomanip>
#include <chrono>

VisualizationDebugger::VisualizationDebugger() 
    : debugMode_(DebugMode::NORMAL) {
    stats_.reset();
}

VisualizationDebugger::~VisualizationDebugger() {
    cv::destroyAllWindows();
}

bool VisualizationDebugger::initialize(std::shared_ptr<DamageDetectionEngine> engine) {
    if (!engine) {
        Utils::logError("检测引擎指针为空");
        return false;
    }
    
    engine_ = engine;
    
    // 创建输出目录
    Utils::createDirectory("output/debug_results");
    Utils::createDirectory("output/debug_intermediate");
    
    Utils::logInfo("可视化调试器初始化完成");
    return true;
}

void VisualizationDebugger::setDebugMode(DebugMode mode) {
    debugMode_ = mode;
    Utils::logInfo("调试模式设置为: " + std::to_string(static_cast<int>(mode)));
}

void VisualizationDebugger::setVisualizationConfig(const VisualizationConfig& config) {
    config_ = config;
}

bool VisualizationDebugger::processAndVisualize(const cv::Mat& image, int cameraId) {
    if (!engine_ || image.empty()) {
        Utils::logError("引擎未初始化或图像为空");
        return false;
    }
    
    auto startTime = std::chrono::high_resolution_clock::now();
    
    // 执行检测
    std::vector<DamageResult> results = engine_->detectDamage(image, cameraId);
    
    auto endTime = std::chrono::high_resolution_clock::now();
    double processingTime = std::chrono::duration<double, std::milli>(endTime - startTime).count();
    
    // 保存结果
    lastImage_ = image.clone();
    lastResults_ = results;
    
    // 更新统计信息
    updateStatistics(results, processingTime);
    
    // 根据调试模式显示结果
    switch (debugMode_) {
        case DebugMode::NORMAL:
            showNormalResults(image, results);
            break;
        case DebugMode::INTERMEDIATE:
            // 这里需要先收集中间结果
            collectIntermediateResults(image, results, processingTime);
            showIntermediateResults(lastIntermediateResults_);
            break;
        case DebugMode::COMPARISON:
            showComparisonResults(image, results);
            break;
        default:
            showNormalResults(image, results);
            break;
    }
    
    return true;
}

bool VisualizationDebugger::loadAndProcess(const std::string& imagePath, int cameraId) {
    cv::Mat image = cv::imread(imagePath);
    if (image.empty()) {
        Utils::logError("无法加载图像: " + imagePath);
        return false;
    }
    
    Utils::logInfo("加载图像: " + imagePath);
    return processAndVisualize(image, cameraId);
}

cv::Mat VisualizationDebugger::drawResults(const cv::Mat& image, 
                                          const std::vector<DamageResult>& results,
                                          const std::vector<DamageType>& enabledTypes) {
    cv::Mat outputImage = image.clone();
    
    for (const auto& result : results) {
        // 检查是否启用该损伤类型的显示
        if (!enabledTypes.empty()) {
            bool enabled = std::find(enabledTypes.begin(), enabledTypes.end(), result.type) != enabledTypes.end();
            if (!enabled) continue;
        }
        
        // 检查置信度阈值
        if (result.confidence < config_.confidenceThreshold) {
            continue;
        }
        
        // 绘制边界框
        if (config_.showBoundingBox) {
            drawBoundingBox(outputImage, result);
        }
        
        // 绘制标签
        if (config_.showDamageType || config_.showConfidence) {
            drawLabel(outputImage, result);
        }
        
        // 绘制中心点
        if (config_.showCenter) {
            cv::circle(outputImage, cv::Point(result.center.x, result.center.y), 3, 
                      getDamageColor(result.type), -1);
        }
    }
    
    return outputImage;
}

void VisualizationDebugger::drawBoundingBox(cv::Mat& image, const DamageResult& result) {
    cv::Scalar color = getDamageColor(result.type);
    cv::rectangle(image, result.boundingBox, color, config_.boxThickness);
}

void VisualizationDebugger::drawLabel(cv::Mat& image, const DamageResult& result) {
    std::string label;
    
    if (config_.showDamageType) {
        label += Utils::damageTypeToString(result.type);
    }
    
    if (config_.showConfidence) {
        if (!label.empty()) label += " ";
        label += std::to_string(static_cast<int>(result.confidence * 100)) + "%";
    }
    
    if (label.empty()) return;
    
    cv::Scalar color = getDamageColor(result.type);
    cv::Point labelPos(result.boundingBox.x, result.boundingBox.y - 5);
    
    // 绘制标签背景
    int baseline = 0;
    cv::Size textSize = cv::getTextSize(label, cv::FONT_HERSHEY_SIMPLEX, 
                                       config_.fontScale, config_.fontThickness, &baseline);
    cv::rectangle(image, 
                 cv::Point(labelPos.x, labelPos.y - textSize.height - baseline),
                 cv::Point(labelPos.x + textSize.width, labelPos.y + baseline),
                 color, -1);
    
    // 绘制文字
    cv::putText(image, label, labelPos, cv::FONT_HERSHEY_SIMPLEX, 
               config_.fontScale, cv::Scalar(255, 255, 255), config_.fontThickness);
}

cv::Scalar VisualizationDebugger::getDamageColor(DamageType type) {
    auto it = config_.damageColors.find(type);
    if (it != config_.damageColors.end()) {
        return it->second;
    }
    return cv::Scalar(128, 128, 128); // 默认灰色
}

cv::Mat VisualizationDebugger::resizeForDisplay(const cv::Mat& image, int maxWidth, int maxHeight) {
    if (image.empty()) return image;
    
    double scale = std::min(static_cast<double>(maxWidth) / image.cols,
                           static_cast<double>(maxHeight) / image.rows);
    
    if (scale >= 1.0) {
        return image; // 不需要缩放
    }
    
    cv::Mat resized;
    cv::resize(image, resized, cv::Size(), scale, scale, cv::INTER_AREA);
    return resized;
}

void VisualizationDebugger::showNormalResults(const cv::Mat& image, 
                                             const std::vector<DamageResult>& results) {
    cv::Mat displayImage = drawResults(image, results);
    cv::Mat resizedImage = resizeForDisplay(displayImage, 1000, 700);
    
    // 创建信息面板
    cv::Mat infoPanel = createInfoPanel(results);
    
    // 合并显示
    cv::Mat combined;
    if (!infoPanel.empty()) {
        cv::hconcat(resizedImage, infoPanel, combined);
    } else {
        combined = resizedImage;
    }
    
    cv::imshow(config_.windowName, combined);
}

void VisualizationDebugger::showComparisonResults(const cv::Mat& image, 
                                                  const std::vector<DamageResult>& results) {
    cv::Mat originalDisplay = resizeForDisplay(image, 500, 400);
    cv::Mat resultDisplay = resizeForDisplay(drawResults(image, results), 500, 400);
    
    // 添加标题
    cv::Mat titleOriginal = cv::Mat::zeros(30, originalDisplay.cols, CV_8UC3);
    cv::putText(titleOriginal, "Original", cv::Point(10, 20), 
               cv::FONT_HERSHEY_SIMPLEX, 0.6, cv::Scalar(255, 255, 255), 2);
    
    cv::Mat titleResult = cv::Mat::zeros(30, resultDisplay.cols, CV_8UC3);
    cv::putText(titleResult, "Detection Result", cv::Point(10, 20), 
               cv::FONT_HERSHEY_SIMPLEX, 0.6, cv::Scalar(255, 255, 255), 2);
    
    // 垂直拼接标题和图像
    cv::Mat originalWithTitle, resultWithTitle;
    cv::vconcat(titleOriginal, originalDisplay, originalWithTitle);
    cv::vconcat(titleResult, resultDisplay, resultWithTitle);
    
    // 水平拼接对比图像
    cv::Mat comparison;
    cv::hconcat(originalWithTitle, resultWithTitle, comparison);
    
    cv::imshow(config_.windowName + " - Comparison", comparison);
}

cv::Mat VisualizationDebugger::createInfoPanel(const std::vector<DamageResult>& results) {
    int panelWidth = 300;
    int panelHeight = 500;
    cv::Mat panel = cv::Mat::zeros(panelHeight, panelWidth, CV_8UC3);
    
    int y = 30;
    int lineHeight = 25;
    
    // 标题
    cv::putText(panel, "Detection Results", cv::Point(10, y), 
               cv::FONT_HERSHEY_SIMPLEX, 0.7, cv::Scalar(255, 255, 255), 2);
    y += lineHeight * 2;
    
    // 统计信息
    cv::putText(panel, "Total: " + std::to_string(results.size()), 
               cv::Point(10, y), cv::FONT_HERSHEY_SIMPLEX, 0.5, cv::Scalar(200, 200, 200), 1);
    y += lineHeight;
    
    // 各类型统计
    std::map<DamageType, int> typeCounts;
    for (const auto& result : results) {
        typeCounts[result.type]++;
    }
    
    for (const auto& pair : typeCounts) {
        std::string typeStr = Utils::damageTypeToString(pair.first);
        std::string countStr = typeStr + ": " + std::to_string(pair.second);
        cv::Scalar color = getDamageColor(pair.first);
        
        cv::putText(panel, countStr, cv::Point(10, y), 
                   cv::FONT_HERSHEY_SIMPLEX, 0.4, color, 1);
        y += lineHeight;
    }
    
    return panel;
}

void VisualizationDebugger::updateStatistics(const std::vector<DamageResult>& results, 
                                             double processingTime) {
    stats_.totalImagesProcessed++;
    stats_.totalDamagesDetected += results.size();
    
    // 更新平均处理时间
    stats_.avgProcessingTime = (stats_.avgProcessingTime * (stats_.totalImagesProcessed - 1) + 
                               processingTime) / stats_.totalImagesProcessed;
    
    // 更新各类型统计
    for (const auto& result : results) {
        stats_.damageTypeCounts[result.type]++;
    }
}

int VisualizationDebugger::batchProcess(const std::vector<std::string>& imagePaths,
                                       const std::string& outputDir) {
    if (!engine_) {
        Utils::logError("检测引擎未初始化");
        return 0;
    }

    Utils::createDirectory(outputDir);
    int successCount = 0;

    for (size_t i = 0; i < imagePaths.size(); ++i) {
        const std::string& imagePath = imagePaths[i];
        Utils::logInfo("处理图像 " + std::to_string(i + 1) + "/" +
                      std::to_string(imagePaths.size()) + ": " + imagePath);

        cv::Mat image = cv::imread(imagePath);
        if (image.empty()) {
            Utils::logWarning("无法加载图像: " + imagePath);
            continue;
        }

        // 执行检测
        auto startTime = std::chrono::high_resolution_clock::now();
        std::vector<DamageResult> results = engine_->detectDamage(image, 0);
        auto endTime = std::chrono::high_resolution_clock::now();
        double processingTime = std::chrono::duration<double, std::milli>(endTime - startTime).count();

        // 更新统计信息
        updateStatistics(results, processingTime);

        // 生成输出文件名
        std::filesystem::path inputPath(imagePath);
        std::string outputFileName = inputPath.stem().string() + "_result.jpg";
        std::string outputPath = outputDir + "/" + outputFileName;

        // 绘制并保存结果
        cv::Mat resultImage = drawResults(image, results);
        if (cv::imwrite(outputPath, resultImage)) {
            successCount++;
        } else {
            Utils::logWarning("保存结果失败: " + outputPath);
        }
    }

    Utils::logInfo("批量处理完成，成功处理 " + std::to_string(successCount) + "/" +
                  std::to_string(imagePaths.size()) + " 张图像");
    return successCount;
}

void VisualizationDebugger::collectIntermediateResults(const cv::Mat& image,
                                                       const std::vector<DamageResult>& results,
                                                       double processingTime) {
    lastIntermediateResults_.originalImage = image.clone();
    lastIntermediateResults_.finalResults = results;
    lastIntermediateResults_.processingTime = processingTime;

    // 这里可以扩展以收集更多中间结果
    // 目前简化实现，实际使用时需要修改DamageDetectionEngine以提供中间结果
    if (engine_) {
        // 获取预处理图像（需要扩展DamageDetectionEngine接口）
        // lastIntermediateResults_.preprocessedImage = engine_->getPreprocessedImage();

        // 获取各算法输出（需要扩展DamageDetectionEngine接口）
        // lastIntermediateResults_.algorithmOutputs = engine_->getAlgorithmOutputs();
    }
}

void VisualizationDebugger::showIntermediateResults(const IntermediateResults& results) {
    if (results.originalImage.empty()) {
        Utils::logWarning("没有中间结果可显示");
        return;
    }

    // 显示原始图像
    cv::Mat originalDisplay = resizeForDisplay(results.originalImage, 400, 300);
    cv::imshow("Original Image", originalDisplay);

    // 显示预处理图像
    if (!results.preprocessedImage.empty()) {
        cv::Mat preprocessedDisplay = resizeForDisplay(results.preprocessedImage, 400, 300);
        cv::imshow("Preprocessed Image", preprocessedDisplay);
    }

    // 显示各算法输出
    for (const auto& pair : results.algorithmOutputs) {
        std::string windowName = "Algorithm Output - " + Utils::damageTypeToString(pair.first);
        cv::Mat algorithmDisplay = resizeForDisplay(pair.second, 400, 300);
        cv::imshow(windowName, algorithmDisplay);
    }

    // 显示最终结果
    cv::Mat finalResult = drawResults(results.originalImage, results.finalResults);
    cv::Mat finalDisplay = resizeForDisplay(finalResult, 400, 300);
    cv::imshow("Final Result", finalDisplay);
}

bool VisualizationDebugger::generateReport(const std::vector<std::vector<DamageResult>>& results,
                                          const std::string& outputPath) {
    try {
        std::ofstream report(outputPath);
        if (!report.is_open()) {
            Utils::logError("无法创建报告文件: " + outputPath);
            return false;
        }

        // 写入报告头部
        report << "缺损检测批量测试报告\n";
        report << "生成时间: " << Utils::getCurrentTimeString() << "\n";
        report << "========================================\n\n";

        // 统计信息
        int totalImages = results.size();
        int totalDamages = 0;
        std::map<DamageType, int> typeCounts;

        for (const auto& imageResults : results) {
            totalDamages += imageResults.size();
            for (const auto& result : imageResults) {
                typeCounts[result.type]++;
            }
        }

        report << "总体统计:\n";
        report << "处理图像数量: " << totalImages << "\n";
        report << "检测到的损伤总数: " << totalDamages << "\n";
        report << "平均每张图像损伤数: " << std::fixed << std::setprecision(2)
               << (totalImages > 0 ? static_cast<double>(totalDamages) / totalImages : 0.0) << "\n\n";

        report << "各类型损伤统计:\n";
        for (const auto& pair : typeCounts) {
            report << Utils::damageTypeToString(pair.first) << ": " << pair.second << "\n";
        }

        report << "\n详细结果:\n";
        report << "========================================\n";

        for (size_t i = 0; i < results.size(); ++i) {
            report << "图像 " << (i + 1) << ":\n";
            const auto& imageResults = results[i];

            if (imageResults.empty()) {
                report << "  未检测到损伤\n";
            } else {
                for (const auto& result : imageResults) {
                    report << "  - " << Utils::damageTypeToString(result.type)
                           << " (置信度: " << std::fixed << std::setprecision(3) << result.confidence
                           << ", 位置: " << result.boundingBox.x << "," << result.boundingBox.y
                           << ", 尺寸: " << result.boundingBox.width << "x" << result.boundingBox.height << ")\n";
                }
            }
            report << "\n";
        }

        report.close();
        Utils::logInfo("报告已生成: " + outputPath);
        return true;

    } catch (const std::exception& e) {
        Utils::logError("生成报告时发生异常: " + std::string(e.what()));
        return false;
    }
}

bool VisualizationDebugger::saveCurrentResult(const std::string& outputPath) {
    if (lastImage_.empty()) {
        Utils::logError("没有可保存的结果");
        return false;
    }

    cv::Mat resultImage = drawResults(lastImage_, lastResults_);

    try {
        // 确保输出目录存在
        std::filesystem::path path(outputPath);
        std::filesystem::create_directories(path.parent_path());

        // 保存图像
        if (!cv::imwrite(outputPath, resultImage)) {
            Utils::logError("保存图像失败: " + outputPath);
            return false;
        }

        Utils::logInfo("结果已保存: " + outputPath);
        return true;
    } catch (const std::exception& e) {
        Utils::logError("保存结果时发生异常: " + std::string(e.what()));
        return false;
    }
}

#endif // USE_OPENCV
