#include "../../include/debug_gui.h"
#include "../../include/common.h"
#ifdef USE_OPENCV
#include <filesystem>
#include <algorithm>
#include <iostream>

// 静态成员定义
const std::string DebugGUI::MAIN_WINDOW = "Damage Detection Debug Tool";
const std::string DebugGUI::CONTROL_WINDOW = "Controls";
const std::string DebugGUI::PARAM_WINDOW = "Parameters";

DebugGUI::DebugGUI() {
    // 初始化默认状态
    state_.confidenceThreshold = 0.3;
    state_.debugMode = VisualizationDebugger::DebugMode::NORMAL;
}

DebugGUI::~DebugGUI() {
    cv::destroyAllWindows();
}

bool DebugGUI::initialize(std::shared_ptr<VisualizationDebugger> debugger) {
    if (!debugger) {
        Utils::logError("可视化调试器指针为空");
        return false;
    }
    
    debugger_ = debugger;
    createWindows();

    // 设置VisualizationDebugger使用与GUI相同的主窗口
    auto config = debugger_->getVisualizationConfig();
    config.windowName = MAIN_WINDOW;
    debugger_->setVisualizationConfig(config);

    Utils::logInfo("调试GUI初始化完成");
    return true;
}

void DebugGUI::createWindows() {
    if (windowsCreated_) return;
    
    // 创建主窗口
    cv::namedWindow(MAIN_WINDOW, cv::WINDOW_AUTOSIZE);
    
    // 创建控制面板窗口
    cv::namedWindow(CONTROL_WINDOW, cv::WINDOW_AUTOSIZE);
    
    // 创建参数调整窗口
    cv::namedWindow(PARAM_WINDOW, cv::WINDOW_AUTOSIZE);
    
    // 设置窗口位置
    cv::moveWindow(MAIN_WINDOW, 100, 100);
    cv::moveWindow(CONTROL_WINDOW, 1100, 100);
    cv::moveWindow(PARAM_WINDOW, 1100, 400);
    
    setupTrackbars();
    windowsCreated_ = true;
}

void DebugGUI::setupTrackbars() {
    // 置信度阈值滑动条
    cv::createTrackbar("Confidence", PARAM_WINDOW, nullptr, 100, onConfidenceThresholdChange, this);
    cv::setTrackbarPos("Confidence", PARAM_WINDOW, static_cast<int>(state_.confidenceThreshold * 100));
    
    // 裂缝检测参数
    cv::createTrackbar("Crack Min Length", PARAM_WINDOW, nullptr, 50, onCrackMinLengthChange, this);
    cv::setTrackbarPos("Crack Min Length", PARAM_WINDOW, static_cast<int>(state_.crackMinLength));
    
    cv::createTrackbar("Crack Max Width", PARAM_WINDOW, nullptr, 20, onCrackMaxWidthChange, this);
    cv::setTrackbarPos("Crack Max Width", PARAM_WINDOW, static_cast<int>(state_.crackMaxWidth));
    
    // 磨损检测参数
    cv::createTrackbar("Wear Area Threshold", PARAM_WINDOW, nullptr, 500, onWearAreaThresholdChange, this);
    cv::setTrackbarPos("Wear Area Threshold", PARAM_WINDOW, static_cast<int>(state_.wearAreaThreshold));
    
    // 凹坑检测参数
    cv::createTrackbar("Dent Min Area", PARAM_WINDOW, nullptr, 200, onDentMinAreaChange, this);
    cv::setTrackbarPos("Dent Min Area", PARAM_WINDOW, static_cast<int>(state_.dentMinArea));
    
    // 鼓包检测参数
    cv::createTrackbar("Bulge Min Area", PARAM_WINDOW, nullptr, 200, onBulgeMinAreaChange, this);
    cv::setTrackbarPos("Bulge Min Area", PARAM_WINDOW, static_cast<int>(state_.bulgeMinArea));
}

// 滑动条回调函数
void DebugGUI::onConfidenceThresholdChange(int value, void* userdata) {
    DebugGUI* gui = static_cast<DebugGUI*>(userdata);
    gui->state_.confidenceThreshold = value / 100.0;
    gui->notifyParameterChange();
}

void DebugGUI::onCrackMinLengthChange(int value, void* userdata) {
    DebugGUI* gui = static_cast<DebugGUI*>(userdata);
    gui->state_.crackMinLength = static_cast<double>(value);
    gui->notifyParameterChange();
}

void DebugGUI::onCrackMaxWidthChange(int value, void* userdata) {
    DebugGUI* gui = static_cast<DebugGUI*>(userdata);
    gui->state_.crackMaxWidth = static_cast<double>(value);
    gui->notifyParameterChange();
}

void DebugGUI::onWearAreaThresholdChange(int value, void* userdata) {
    DebugGUI* gui = static_cast<DebugGUI*>(userdata);
    gui->state_.wearAreaThreshold = static_cast<double>(value);
    gui->notifyParameterChange();
}

void DebugGUI::onDentMinAreaChange(int value, void* userdata) {
    DebugGUI* gui = static_cast<DebugGUI*>(userdata);
    gui->state_.dentMinArea = static_cast<double>(value);
    gui->notifyParameterChange();
}

void DebugGUI::onBulgeMinAreaChange(int value, void* userdata) {
    DebugGUI* gui = static_cast<DebugGUI*>(userdata);
    gui->state_.bulgeMinArea = static_cast<double>(value);
    gui->notifyParameterChange();
}

int DebugGUI::run() {
    if (!debugger_) {
        Utils::logError("调试器未初始化");
        return -1;
    }

    Utils::logInfo("启动调试GUI，按ESC键退出");

    // 检查显示环境
    std::string display = std::getenv("DISPLAY") ? std::getenv("DISPLAY") : "";
    std::string waylandDisplay = std::getenv("WAYLAND_DISPLAY") ? std::getenv("WAYLAND_DISPLAY") : "";
    std::string sessionType = std::getenv("XDG_SESSION_TYPE") ? std::getenv("XDG_SESSION_TYPE") : "";

    Utils::logInfo("显示环境信息:");
    Utils::logInfo("  DISPLAY = " + (display.empty() ? "未设置" : display));
    Utils::logInfo("  WAYLAND_DISPLAY = " + (waylandDisplay.empty() ? "未设置" : waylandDisplay));
    Utils::logInfo("  XDG_SESSION_TYPE = " + (sessionType.empty() ? "未设置" : sessionType));

    // 检查是否有GUI环境
    try {
        // 显示初始控制面板
        updateDisplay();

        // 等待更长时间确保窗口创建完成
        cv::waitKey(500);

        // 使用更宽松的窗口检测逻辑
        bool windowsCreated = false;
        try {
            // 尝试检查窗口属性，但不依赖返回值
            cv::getWindowProperty(MAIN_WINDOW, cv::WND_PROP_VISIBLE);
            cv::getWindowProperty(CONTROL_WINDOW, cv::WND_PROP_VISIBLE);
            cv::getWindowProperty(PARAM_WINDOW, cv::WND_PROP_VISIBLE);
            windowsCreated = true;
            Utils::logInfo("窗口属性检查通过");
        } catch (const cv::Exception& e) {
            Utils::logWarning("窗口属性检查异常，但继续尝试: " + std::string(e.what()));
            // 即使检查失败，也尝试继续运行
            windowsCreated = true;
        }

        if (!windowsCreated) {
            Utils::logError("无法创建GUI窗口");
            Utils::logInfo("环境诊断建议:");
            Utils::logInfo("1. 确保在图形桌面环境中运行");
            Utils::logInfo("2. 如果使用Wayland，尝试: export GDK_BACKEND=x11");
            Utils::logInfo("3. 如果在SSH中，使用: ssh -X");
            Utils::logInfo("4. 运行诊断脚本: ./scripts/diagnose_gui.sh");
            return -1;
        }

        Utils::logInfo("GUI初始化完成，窗口应该已显示");
        Utils::logInfo("如果看不到窗口，请检查任务栏或尝试Alt+Tab切换");
    } catch (const cv::Exception& e) {
        Utils::logError("OpenCV GUI错误: " + std::string(e.what()));
        Utils::logInfo("这可能是OpenCV GUI依赖问题，请检查libgtk或libqt安装");
        return -1;
    }
    
    while (true) {
        int key = cv::waitKey(30) & 0xFF;
        
        switch (key) {
            case 27: // ESC键退出
                return 0;
                
            case 'o': // 打开文件
            case 'O':
                handleLoadImage();
                break;
                
            case 'd': // 打开目录
            case 'D':
                handleLoadDirectory();
                break;
                
            case 'p': // 上一张图像
            case 'P':
                handlePreviousImage();
                break;
                
            case 'n': // 下一张图像
            case 'N':
                handleNextImage();
                break;
                
            case 's': // 保存结果
            case 'S':
                handleSaveResult();
                break;
                
            case 'b': // 批量处理
            case 'B':
                handleBatchProcess();
                break;
                
            case 'r': // 重置参数
            case 'R':
                handleResetParameters();
                break;
                
            case 'm': // 切换调试模式
            case 'M':
                handleDebugModeChange();
                break;
                
            case '1': // 切换裂缝显示
                handleToggleDamageType(DamageType::CRACK);
                break;
                
            case '2': // 切换磨损显示
                handleToggleDamageType(DamageType::WEAR);
                break;
                
            case '3': // 切换刮伤显示
                handleToggleDamageType(DamageType::SCRATCH);
                break;
                
            case '4': // 切换凹坑显示
                handleToggleDamageType(DamageType::PIT);
                break;
                
            case '5': // 切换鼓包显示
                handleToggleDamageType(DamageType::BULGE);
                break;
                
            case '6': // 切换老化显示
                handleToggleDamageType(DamageType::AGING);
                break;
                
            case '7': // 切换安装损伤显示
                handleToggleDamageType(DamageType::INSTALL_DAMAGE);
                break;
        }
        
        // 检查主要窗口是否被关闭
        try {
            // 如果任何一个主要窗口被关闭，就退出
            if (cv::getWindowProperty(MAIN_WINDOW, cv::WND_PROP_VISIBLE) < 1 ||
                cv::getWindowProperty(CONTROL_WINDOW, cv::WND_PROP_VISIBLE) < 1 ||
                cv::getWindowProperty(PARAM_WINDOW, cv::WND_PROP_VISIBLE) < 1) {
                Utils::logInfo("检测到窗口关闭，退出GUI");
                break;
            }
        } catch (const cv::Exception& e) {
            Utils::logWarning("窗口状态检查异常，退出GUI: " + std::string(e.what()));
            break;
        }
    }
    
    return 0;
}

void DebugGUI::updateDisplay() {
    // 更新控制面板
    cv::Mat controlPanel = createControlPanelImage();
    cv::imshow(CONTROL_WINDOW, controlPanel);

    // 更新参数面板
    cv::Mat paramPanel = createParameterPanelImage();
    cv::imshow(PARAM_WINDOW, paramPanel);

    // 如果主窗口还没有内容，显示欢迎信息
    if (state_.currentImagePath.empty()) {
        cv::Mat welcomeImage = createWelcomeImage();
        cv::imshow(MAIN_WINDOW, welcomeImage);
    }
}

cv::Mat DebugGUI::createControlPanelImage() {
    cv::Mat panel = cv::Mat::zeros(400, 300, CV_8UC3);
    int y = 30;
    int lineHeight = 25;
    
    // 标题
    cv::putText(panel, "Control Panel", cv::Point(10, y), 
               cv::FONT_HERSHEY_SIMPLEX, 0.7, cv::Scalar(255, 255, 255), 2);
    y += lineHeight * 2;
    
    // 快捷键说明
    cv::putText(panel, "Hotkeys:", cv::Point(10, y), 
               cv::FONT_HERSHEY_SIMPLEX, 0.5, cv::Scalar(200, 200, 200), 1);
    y += lineHeight;
    
    std::vector<std::string> hotkeys = {
        "O - Open Image",
        "D - Open Directory", 
        "P - Previous Image",
        "N - Next Image",
        "S - Save Result",
        "B - Batch Process",
        "R - Reset Parameters",
        "M - Toggle Debug Mode",
        "1-7 - Toggle Damage Types",
        "ESC - Exit"
    };
    
    for (const auto& hotkey : hotkeys) {
        cv::putText(panel, hotkey, cv::Point(10, y), 
                   cv::FONT_HERSHEY_SIMPLEX, 0.4, cv::Scalar(150, 150, 150), 1);
        y += lineHeight - 5;
    }
    
    return panel;
}

cv::Mat DebugGUI::createParameterPanelImage() {
    cv::Mat panel = cv::Mat::zeros(300, 300, CV_8UC3);
    int y = 30;
    int lineHeight = 20;
    
    // 标题
    cv::putText(panel, "Current Parameters", cv::Point(10, y), 
               cv::FONT_HERSHEY_SIMPLEX, 0.6, cv::Scalar(255, 255, 255), 2);
    y += lineHeight * 2;
    
    // 显示当前参数值
    std::vector<std::string> params = {
        "Confidence: " + std::to_string(static_cast<int>(state_.confidenceThreshold * 100)) + "%",
        "Crack Min Length: " + std::to_string(static_cast<int>(state_.crackMinLength)),
        "Crack Max Width: " + std::to_string(static_cast<int>(state_.crackMaxWidth)),
        "Wear Area Threshold: " + std::to_string(static_cast<int>(state_.wearAreaThreshold)),
        "Dent Min Area: " + std::to_string(static_cast<int>(state_.dentMinArea)),
        "Bulge Min Area: " + std::to_string(static_cast<int>(state_.bulgeMinArea))
    };
    
    for (const auto& param : params) {
        cv::putText(panel, param, cv::Point(10, y), 
                   cv::FONT_HERSHEY_SIMPLEX, 0.4, cv::Scalar(200, 200, 200), 1);
        y += lineHeight;
    }
    
    return panel;
}

cv::Mat DebugGUI::createWelcomeImage() {
    cv::Mat welcome = cv::Mat::zeros(600, 800, CV_8UC3);
    int y = 80;
    int lineHeight = 40;

    // 标题
    cv::putText(welcome, "Damage Detection Debug Tool", cv::Point(150, y),
               cv::FONT_HERSHEY_SIMPLEX, 1.2, cv::Scalar(255, 255, 255), 2);
    y += lineHeight * 2;

    // 使用说明
    cv::putText(welcome, "Quick Start Guide:", cv::Point(50, y),
               cv::FONT_HERSHEY_SIMPLEX, 0.8, cv::Scalar(200, 200, 200), 2);
    y += lineHeight;

    std::vector<std::string> instructions = {
        "Press 'O' - Open image file",
        "Press 'D' - Open image directory",
        "Press 'P/N' - Previous/Next image",
        "Press 'S' - Save current result",
        "Press 'B' - Start batch processing",
        "Press 'M' - Toggle debug mode",
        "Press '1-7' - Toggle damage type display",
        "Press 'R' - Reset parameters",
        "Press 'ESC' - Exit application"
    };

    for (const auto& instruction : instructions) {
        cv::putText(welcome, instruction, cv::Point(80, y),
                   cv::FONT_HERSHEY_SIMPLEX, 0.6, cv::Scalar(150, 255, 150), 1);
        y += 35;
    }

    // 底部提示
    cv::putText(welcome, "Start by pressing 'O' to open an image file",
               cv::Point(200, 550), cv::FONT_HERSHEY_SIMPLEX, 0.7,
               cv::Scalar(100, 200, 255), 2);

    return welcome;
}

void DebugGUI::notifyParameterChange() {
    if (parameterChangeCallback_) {
        parameterChangeCallback_(state_);
    }
    updateDisplay();
}

void DebugGUI::notifyImageChange() {
    if (imageChangeCallback_ && !state_.currentImagePath.empty()) {
        imageChangeCallback_(state_.currentImagePath);
    }
}

// 事件处理方法实现
void DebugGUI::handleLoadImage() {
    std::string imagePath = SimpleFileBrowser::selectFile("Select Image File");
    if (!imagePath.empty()) {
        loadImage(imagePath);
    }
}

void DebugGUI::handleLoadDirectory() {
    std::string dirPath = SimpleFileBrowser::selectDirectory("Select Image Directory");
    if (!dirPath.empty()) {
        int count = loadImageDirectory(dirPath);
        Utils::logInfo("加载了 " + std::to_string(count) + " 张图像");
    }
}

void DebugGUI::handlePreviousImage() {
    if (!state_.imageList.empty() && state_.currentImageIndex > 0) {
        state_.currentImageIndex--;
        state_.currentImagePath = state_.imageList[state_.currentImageIndex];
        notifyImageChange();
        Utils::logInfo("切换到图像: " + std::to_string(state_.currentImageIndex + 1) + "/" +
                      std::to_string(state_.imageList.size()));
    }
}

void DebugGUI::handleNextImage() {
    if (!state_.imageList.empty() && state_.currentImageIndex < static_cast<int>(state_.imageList.size()) - 1) {
        state_.currentImageIndex++;
        state_.currentImagePath = state_.imageList[state_.currentImageIndex];
        notifyImageChange();
        Utils::logInfo("切换到图像: " + std::to_string(state_.currentImageIndex + 1) + "/" +
                      std::to_string(state_.imageList.size()));
    }
}

void DebugGUI::handleSaveResult() {
    if (debugger_ && !state_.currentImagePath.empty()) {
        std::filesystem::path inputPath(state_.currentImagePath);
        std::string outputPath = "output/debug_results/" + inputPath.stem().string() + "_result.jpg";

        if (debugger_->saveCurrentResult(outputPath)) {
            Utils::logInfo("结果已保存: " + outputPath);
        }
    }
}

void DebugGUI::handleBatchProcess() {
    if (!state_.imageList.empty() && batchProcessCallback_) {
        Utils::logInfo("开始批量处理 " + std::to_string(state_.imageList.size()) + " 张图像");
        batchProcessCallback_(state_.imageList, state_.batchOutputDir);
    }
}

void DebugGUI::handleResetParameters() {
    // 重置为默认参数
    state_.confidenceThreshold = 0.3;
    state_.crackMinLength = 5.0;
    state_.crackMaxWidth = 3.0;
    state_.wearAreaThreshold = 100.0;
    state_.dentMinArea = 50.0;
    state_.bulgeMinArea = 50.0;

    // 更新滑动条位置
    cv::setTrackbarPos("Confidence", PARAM_WINDOW, static_cast<int>(state_.confidenceThreshold * 100));
    cv::setTrackbarPos("Crack Min Length", PARAM_WINDOW, static_cast<int>(state_.crackMinLength));
    cv::setTrackbarPos("Crack Max Width", PARAM_WINDOW, static_cast<int>(state_.crackMaxWidth));
    cv::setTrackbarPos("Wear Area Threshold", PARAM_WINDOW, static_cast<int>(state_.wearAreaThreshold));
    cv::setTrackbarPos("Dent Min Area", PARAM_WINDOW, static_cast<int>(state_.dentMinArea));
    cv::setTrackbarPos("Bulge Min Area", PARAM_WINDOW, static_cast<int>(state_.bulgeMinArea));

    notifyParameterChange();
    Utils::logInfo("参数已重置为默认值");
}

void DebugGUI::handleToggleDamageType(DamageType type) {
    auto it = std::find(state_.enabledDamageTypes.begin(), state_.enabledDamageTypes.end(), type);
    if (it != state_.enabledDamageTypes.end()) {
        state_.enabledDamageTypes.erase(it);
        Utils::logInfo("隐藏损伤类型: " + Utils::damageTypeToString(type));
    } else {
        state_.enabledDamageTypes.push_back(type);
        Utils::logInfo("显示损伤类型: " + Utils::damageTypeToString(type));
    }
    notifyParameterChange();
}

void DebugGUI::handleDebugModeChange() {
    switch (state_.debugMode) {
        case VisualizationDebugger::DebugMode::NORMAL:
            state_.debugMode = VisualizationDebugger::DebugMode::INTERMEDIATE;
            Utils::logInfo("切换到中间结果模式");
            break;
        case VisualizationDebugger::DebugMode::INTERMEDIATE:
            state_.debugMode = VisualizationDebugger::DebugMode::COMPARISON;
            Utils::logInfo("切换到对比模式");
            break;
        case VisualizationDebugger::DebugMode::COMPARISON:
            state_.debugMode = VisualizationDebugger::DebugMode::NORMAL;
            Utils::logInfo("切换到普通模式");
            break;
        default:
            state_.debugMode = VisualizationDebugger::DebugMode::NORMAL;
            break;
    }

    if (debugger_) {
        debugger_->setDebugMode(state_.debugMode);
    }
    notifyParameterChange();
}

bool DebugGUI::loadImage(const std::string& imagePath) {
    if (!std::filesystem::exists(imagePath)) {
        Utils::logError("图像文件不存在: " + imagePath);
        return false;
    }

    state_.currentImagePath = imagePath;
    state_.imageList.clear();
    state_.imageList.push_back(imagePath);
    state_.currentImageIndex = 0;

    notifyImageChange();
    Utils::logInfo("加载图像: " + imagePath);
    return true;
}

int DebugGUI::loadImageDirectory(const std::string& dirPath) {
    state_.imageList = SimpleFileBrowser::getImageFiles(dirPath);
    if (!state_.imageList.empty()) {
        state_.currentImageIndex = 0;
        state_.currentImagePath = state_.imageList[0];
        notifyImageChange();
    }
    return static_cast<int>(state_.imageList.size());
}

void DebugGUI::setParameterChangeCallback(ParameterChangeCallback callback) {
    parameterChangeCallback_ = callback;
}

void DebugGUI::setImageChangeCallback(ImageChangeCallback callback) {
    imageChangeCallback_ = callback;
}

void DebugGUI::setBatchProcessCallback(BatchProcessCallback callback) {
    batchProcessCallback_ = callback;
}

// SimpleFileBrowser实现
std::string SimpleFileBrowser::selectFile(const std::string& title, const std::string& initialDir) {
    // 简化实现：使用控制台输入
    std::cout << title << " (输入文件路径): ";
    std::string path;
    std::getline(std::cin, path);

    if (!path.empty() && std::filesystem::exists(path) && isImageFile(path)) {
        return path;
    }
    return "";
}

std::string SimpleFileBrowser::selectDirectory(const std::string& title, const std::string& initialDir) {
    // 简化实现：使用控制台输入
    std::cout << title << " (输入目录路径): ";
    std::string path;
    std::getline(std::cin, path);

    if (!path.empty() && std::filesystem::exists(path) && std::filesystem::is_directory(path)) {
        return path;
    }
    return "";
}

std::vector<std::string> SimpleFileBrowser::getImageFiles(const std::string& directory) {
    std::vector<std::string> imageFiles;

    try {
        for (const auto& entry : std::filesystem::directory_iterator(directory)) {
            if (entry.is_regular_file() && isImageFile(entry.path().string())) {
                imageFiles.push_back(entry.path().string());
            }
        }

        // 排序文件名
        std::sort(imageFiles.begin(), imageFiles.end());
    } catch (const std::exception& e) {
        Utils::logError("读取目录失败: " + std::string(e.what()));
    }

    return imageFiles;
}

bool SimpleFileBrowser::isImageFile(const std::string& filename) {
    std::string ext = std::filesystem::path(filename).extension().string();
    std::transform(ext.begin(), ext.end(), ext.begin(), ::tolower);

    return ext == ".jpg" || ext == ".jpeg" || ext == ".png" ||
           ext == ".bmp" || ext == ".tiff" || ext == ".tif";
}

#endif // USE_OPENCV
